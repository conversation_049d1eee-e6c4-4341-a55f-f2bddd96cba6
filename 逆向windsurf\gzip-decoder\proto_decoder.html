<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Protobuf 解码器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }



        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            opacity: 0.8;
            font-size: 1.1em;
        }



        .output-panel {
            padding: 30px;
            background: white;
        }

        .panel-title {
            font-size: 1.4em;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .panel-title::before {
            content: '';
            width: 4px;
            height: 20px;
            background: #3498db;
            border-radius: 2px;
        }

        .result-controls {
            display: flex;
            align-items: center;
        }

        .control-btn {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 6px 10px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
            margin-left: 5px;
        }

        .control-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }

        .control-btn:active {
            background: #dee2e6;
        }

        .copy-btn {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 3px;
            padding: 2px 6px;
            cursor: pointer;
            font-size: 12px;
            color: #1976d2;
            margin-left: 8px;
            transition: all 0.2s ease;
            display: inline-block;
        }

        .copy-btn:hover {
            background: #bbdefb;
            border-color: #1976d2;
        }

        .copy-btn:active {
            background: #90caf9;
        }

        .copy-btn.copied {
            background: #c8e6c9;
            border-color: #4caf50;
            color: #2e7d32;
        }

        .byte-range {
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-family: 'Courier New', monospace;
            margin-left: 8px;
            border: 1px solid #bbdefb;
        }

        /* 结果布局样式 */
        .result-layout {
            display: flex;
            gap: 20px;
            height: 600px;
            max-height: 70vh;
        }

        .result-main {
            flex: 1;
            min-width: 0;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow-y: auto;
            padding: 20px;
        }

        .result-toc {
            width: 300px;
            height: 100%;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .toc-header {
            background: #e9ecef;
            padding: 12px 16px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .toc-header h4 {
            margin: 0;
            font-size: 14px;
            color: #495057;
        }

        .toc-close {
            background: none;
            border: none;
            font-size: 16px;
            cursor: pointer;
            color: #6c757d;
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .toc-close:hover {
            color: #495057;
        }

        .toc-content {
            padding: 8px;
            height: calc(100% - 50px); /* 减去头部高度 */
            overflow-y: auto;
        }

        .toc-item {
            padding: 4px 8px;
            margin: 2px 0;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            line-height: 1.4;
            transition: background-color 0.2s;
        }

        .toc-item:hover {
            background: #e9ecef;
        }

        .toc-item.active {
            background: #007bff;
            color: white;
        }

        .toc-field-number {
            color: #dc3545;
            font-weight: bold;
            margin-right: 4px;
        }

        .toc-field-type {
            color: #fd7e14;
            font-size: 11px;
            margin-left: 4px;
        }

        .toc-nested {
            margin-left: 16px;
            border-left: 1px solid #dee2e6;
            padding-left: 8px;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .result-toc {
                width: 250px;
            }
        }

        @media (max-width: 768px) {
            .result-layout {
                flex-direction: column;
                height: auto;
                max-height: none;
            }

            .result-main {
                height: 400px;
                order: 1;
            }

            .result-toc {
                width: 100%;
                height: 300px;
                order: -1;
            }
        }

        /* 全局Header样式 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
            font-weight: 700;
        }

        .header p {
            margin: 0;
            font-size: 1.1em;
            opacity: 0.9;
        }

        /* TAB页样式 */
        .tab-container {
            width: 100%;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .tab-header {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 0;
            flex-shrink: 0;
        }

        .tab-button {
            padding: 15px 30px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            color: #6c757d;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            color: #2c3e50;
            border-bottom-color: #3498db;
            background: white;
        }

        .tab-button:hover:not(.active) {
            color: #495057;
            background: #e9ecef;
        }

        .tab-content {
            flex: 1;
            display: none;
            overflow: auto;
        }

        .tab-content.active {
            display: block;
        }

        .input-tab-content {
            height: 100%;
            padding: 30px;
            background: white;
            overflow: auto;
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
        }

        .result-tab {
            height: 100%;
            padding: 30px;
            background: white;
            overflow: auto;
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        .input-tabs {
            display: flex;
            margin-bottom: 15px;
            background: #e9ecef;
            border-radius: 8px;
            padding: 4px;
        }

        .format-tab {
            flex: 1;
            padding: 10px 15px;
            text-align: center;
            background: transparent;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .format-tab.active {
            background: white;
            color: #3498db;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .input-content {
            position: relative;
        }

        textarea {
            width: 100%;
            min-height: 200px;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        textarea:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
            background: white;
        }

        .decode-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .decode-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .decode-btn:active {
            transform: translateY(0);
        }

        .decode-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .result-container {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            min-height: 400px;
            max-height: 600px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-y: auto;
            overflow-x: hidden;
            border: 1px solid #e9ecef;
        }

        .proto-field {
            margin: 5px 0;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.2s ease;
        }

        .proto-field:hover {
            background-color: rgba(52, 152, 219, 0.1);
        }

        .field-header {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            user-select: none;
        }

        .field-toggle {
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #3498db;
            color: white;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }

        .field-number {
            background: #e74c3c;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            min-width: 20px;
            text-align: center;
        }

        .field-type {
            background: #f39c12;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .field-type:hover {
            background: #e67e22;
        }

        .field-value {
            margin-left: 24px;
            padding: 8px 12px;
            word-break: break-all;
            background: #f8f9fa;
            border-radius: 4px;
            margin-top: 5px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            max-width: 100%;
            overflow-wrap: break-word;
        }

        .field-children {
            margin-left: 24px;
            border-left: 2px solid #ecf0f1;
            padding-left: 15px;
            margin-top: 5px;
        }

        .field-children.collapsed {
            display: none;
        }

        .type-selector {
            position: absolute;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            min-width: 120px;
        }

        .type-option {
            padding: 8px 12px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            font-size: 12px;
        }

        .type-option:hover {
            background-color: #f8f9fa;
        }

        .type-option:first-child {
            border-radius: 4px 4px 0 0;
        }

        .type-option:last-child {
            border-radius: 0 0 4px 4px;
        }

        .error-message {
            background: #e74c3c;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .success-message {
            background: #27ae60;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            color: #7f8c8d;
            font-style: italic;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #ecf0f1;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #7f8c8d;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .stat-value {
            font-weight: bold;
            color: #2c3e50;
        }


    </style>
</head>
<body>
    <!-- 全局Header -->
    <div class="header">
        <h1>🔍 Protobuf 解码器</h1>
        <p>专业的 Protobuf 数据解析工具，支持 Hex 和 Base64 格式输入</p>
    </div>

    <div class="tab-container">
        <!-- TAB页头部 -->
        <div class="tab-header">
            <button class="tab-button active" id="inputTab" onclick="uiController.switchTab('input')">
                📝 数据输入
            </button>
            <button class="tab-button" id="resultTab" onclick="uiController.switchTab('result')">
                📊 解码结果
            </button>
        </div>

        <!-- 数据输入TAB页 -->
        <div class="tab-content active" id="inputTabContent">
            <div class="input-tab-content">
                <div class="panel-title">📝 数据输入</div>

                <div class="input-group">
                    <div class="input-tabs">
                        <button class="format-tab active" data-type="hex">Hex 格式</button>
                        <button class="format-tab" data-type="base64">Base64 格式</button>
                    </div>

                    <div class="input-content">
                        <textarea id="inputData" placeholder="请输入 Hex 格式的数据，例如：08961a12047465737418c801"></textarea>
                    </div>
                </div>

                <div id="messageContainer"></div>

                <button class="decode-btn" id="decodeBtn">🚀 开始解码</button>
            </div>
        </div>

        <!-- 解码结果TAB页 -->
        <div class="tab-content" id="resultTabContent">
            <div class="result-tab">
                <div class="panel-title">
                    📊 解码结果
                    <div class="result-controls" id="resultControls" style="display: none; margin-left: auto; gap: 10px;">
                        <button class="control-btn" id="expandAllBtn" title="展开所有">➕</button>
                        <button class="control-btn" id="collapseAllBtn" title="折叠所有">➖</button>
                        <button class="control-btn" id="toggleTocBtn" title="显示/隐藏目录">📑</button>
                    </div>
                </div>

                <div id="statsContainer" class="stats" style="display: none;">
                    <div class="stat-item">
                        <span>📦 字段数量:</span>
                        <span class="stat-value" id="fieldCount">0</span>
                    </div>
                    <div class="stat-item">
                        <span>📏 数据长度:</span>
                        <span class="stat-value" id="dataLength">0</span>
                    </div>
                    <div class="stat-item">
                        <span>⏱️ 解码时间:</span>
                        <span class="stat-value" id="decodeTime">0ms</span>
                    </div>
                </div>

                <div class="result-layout">
                    <div class="result-main" id="resultContainer">
                        <div class="loading" style="display: none;" id="loadingIndicator">
                            <div class="spinner"></div>
                            <span>正在解码中...</span>
                        </div>
                        <div style="text-align: center; color: #7f8c8d; padding: 50px;">
                            请输入数据并点击解码按钮开始分析
                        </div>
                    </div>
                    <div class="result-toc" id="resultToc" style="display: none;">
                        <div class="toc-header">
                            <h4>📑 字段目录</h4>
                            <button class="toc-close" id="tocCloseBtn">✕</button>
                        </div>
                        <div class="toc-content" id="tocContent"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Protobuf 线类型定义
        const WIRE_TYPES = {
            0: 'VARINT',
            1: 'FIXED64',
            2: 'LENGTH_DELIMITED',
            3: 'START_GROUP',
            4: 'END_GROUP',
            5: 'FIXED32'
        };

        // 字段类型定义
        const FIELD_TYPES = {
            'VARINT': ['int32', 'int64', 'uint32', 'uint64', 'sint32', 'sint64', 'bool', 'enum'],
            'FIXED64': ['fixed64', 'sfixed64', 'double'],
            'LENGTH_DELIMITED': ['string', 'bytes', 'message', 'repeated'],
            'FIXED32': ['fixed32', 'sfixed32', 'float']
        };

        class ProtobufDecoder {
            constructor() {
                this.data = null;
                this.position = 0;
                this.fieldOverrides = new Map(); // 存储用户自定义的字段类型
            }

            // 将 Hex 字符串转换为字节数组
            hexToBytes(hex) {
                // 清理输入：移除空格、换行符、制表符等
                hex = hex.replace(/[\s\r\n\t]/g, '');

                // 移除可能的0x前缀
                if (hex.toLowerCase().startsWith('0x')) {
                    hex = hex.slice(2);
                }

                if (hex.length === 0) {
                    throw new Error('Hex 字符串不能为空');
                }

                if (hex.length % 2 !== 0) {
                    throw new Error(`Hex 字符串长度必须为偶数，当前长度: ${hex.length}`);
                }

                // 验证是否只包含有效的十六进制字符
                if (!/^[0-9a-fA-F]+$/.test(hex)) {
                    throw new Error('Hex 字符串包含无效字符，只能包含 0-9, a-f, A-F');
                }

                const bytes = [];
                for (let i = 0; i < hex.length; i += 2) {
                    const hexPair = hex.substr(i, 2);
                    const byte = parseInt(hexPair, 16);
                    if (isNaN(byte)) {
                        throw new Error(`无效的 Hex 字符对: ${hexPair} (位置: ${i})`);
                    }
                    bytes.push(byte);
                }
                return new Uint8Array(bytes);
            }

            // 将 Base64 字符串转换为字节数组
            base64ToBytes(base64) {
                try {
                    const binaryString = atob(base64);
                    const bytes = new Uint8Array(binaryString.length);
                    for (let i = 0; i < binaryString.length; i++) {
                        bytes[i] = binaryString.charCodeAt(i);
                    }
                    return bytes;
                } catch (e) {
                    throw new Error('无效的 Base64 格式');
                }
            }

            // 读取 varint
            readVarint() {
                let result = 0;
                let shift = 0;

                while (this.position < this.data.length) {
                    const byte = this.data[this.position++];

                    // 处理大数值时使用更安全的方法
                    if (shift < 32) {
                        result |= (byte & 0x7F) << shift;
                    } else {
                        // 对于超过32位的情况，使用不同的处理方式
                        result += (byte & 0x7F) * Math.pow(2, shift);
                    }

                    if ((byte & 0x80) === 0) {
                        // 确保结果为正整数
                        return result >>> 0;
                    }

                    shift += 7;
                    if (shift >= 35) { // 放宽限制，支持更大的varint
                        throw new Error('Varint 过长');
                    }
                }

                throw new Error('意外的数据结束');
            }

            // 读取固定32位
            readFixed32() {
                if (this.position + 4 > this.data.length) {
                    throw new Error('数据不足，无法读取 Fixed32');
                }

                const result = this.data[this.position] |
                              (this.data[this.position + 1] << 8) |
                              (this.data[this.position + 2] << 16) |
                              (this.data[this.position + 3] << 24);

                this.position += 4;
                return result >>> 0; // 转换为无符号整数
            }

            // 读取固定64位
            readFixed64() {
                if (this.position + 8 > this.data.length) {
                    throw new Error('数据不足，无法读取 Fixed64');
                }

                const low = this.readFixed32();
                const high = this.readFixed32();

                return { low, high, toString: () => `${high}${low.toString(16).padStart(8, '0')}` };
            }

            // 读取长度分隔的数据
            readLengthDelimited() {
                const length = this.readVarint();

                if (this.position + length > this.data.length) {
                    throw new Error('数据不足，无法读取指定长度的数据');
                }

                const result = this.data.slice(this.position, this.position + length);
                this.position += length;

                return result;
            }

            // 尝试将字节数组解析为字符串
            tryParseString(bytes) {
                try {
                    const decoder = new TextDecoder('utf-8', { fatal: true });
                    return decoder.decode(bytes);
                } catch (e) {
                    return null;
                }
            }

            // 尝试将字节数组解析为嵌套消息
            tryParseMessage(bytes, parentPath = '') {
                if (!bytes || bytes.length === 0) {
                    return null;
                }

                try {
                    const originalData = this.data;
                    const originalPosition = this.position;

                    this.data = bytes;
                    this.position = 0;

                    const fields = [];
                    let fieldCount = 0;
                    const maxFields = 100; // 防止嵌套消息解析过深

                    while (this.position < this.data.length && fieldCount < maxFields) {
                        try {
                            const currentPos = this.position;
                            const field = this.readField(parentPath);

                            if (field) {
                                fields.push(field);
                                fieldCount++;
                            } else {
                                break;
                            }

                            // 防止位置没有前进
                            if (this.position === currentPos) {
                                console.warn(`嵌套消息解析时位置没有前进，位置: ${this.position}`);
                                break;
                            }

                        } catch (fieldError) {
                            console.warn(`嵌套消息字段解析失败:`, fieldError.message);
                            // 尝试跳过当前字节
                            this.position++;
                            if (this.position >= this.data.length) {
                                break;
                            }
                        }
                    }

                    // 检查解析结果的有效性
                    const hasValidFields = fields.length > 0;
                    const currentPos = this.position; // 保存当前解析位置
                    const parsedRatio = currentPos / bytes.length;

                    // 恢复原始状态
                    this.data = originalData;
                    this.position = originalPosition;

                    // 简化消息验证条件 - 更宽松的解析比例要求
                    const isValidMessage = hasValidFields &&
                                         fields.length >= 1 &&
                                         parsedRatio >= 0.7; // 进一步放宽解析比例要求

                    console.log(`嵌套消息解析结果: 字段数=${fields.length}, 解析比例=${(parsedRatio * 100).toFixed(1)}%, 有效=${isValidMessage}`);

                    return isValidMessage ? fields : null;

                } catch (e) {
                    // 确保恢复原始状态
                    this.data = originalData;
                    this.position = originalPosition;
                    console.warn(`嵌套消息解析异常:`, e.message);
                    return null;
                }
            }

            // 判断解析结果是否像有效的 Protobuf 消息
            isLikelyProtobufMessage(fields, originalBytes) {
                if (!fields || fields.length === 0) {
                    return false;
                }

                // 检查字段编号的合理性
                const fieldNumbers = fields.map(f => f.number);
                const hasReasonableFieldNumbers = fieldNumbers.every(num => num > 0 && num < 536870912); // 2^29

                // 检查是否有重复的字段编号（在某些情况下是合理的）
                const uniqueNumbers = new Set(fieldNumbers);
                const hasRepeatedFields = uniqueNumbers.size < fieldNumbers.length;

                // 检查线类型的分布
                const wireTypes = fields.map(f => f.wireType);
                const hasValidWireTypes = wireTypes.every(wt => [0, 1, 2, 5].includes(wt));

                // 检查是否包含明显的字符串特征
                const stringValue = this.tryParseString(originalBytes);
                const looksLikeString = stringValue &&
                    stringValue.length > 0 &&
                    /^[a-zA-Z0-9\s\-_\.@\/:=]+$/.test(stringValue.trim());

                // 如果看起来像字符串，则很可能不是消息（更严格的字符串保护）
                if (looksLikeString && originalBytes.length < 100) { // 短数据更可能是字符串
                    console.log(`数据更像字符串而非消息: "${stringValue}"`);
                    return false;
                }

                const score = (hasReasonableFieldNumbers ? 1 : 0) +
                             (hasValidWireTypes ? 1 : 0) +
                             (fields.length >= 1 ? 1 : 0) + // 放宽：1个字段也给分
                             (!looksLikeString ? 1 : 0);

                console.log(`消息可能性评分: ${score}/4, 字段数: ${fields.length}, 看起来像字符串: ${looksLikeString}`);

                return score >= 2; // 放宽：只需要2分就认为是有效消息
            }

            // 读取单个字段
            readField(parentPath = '') {
                if (this.position >= this.data.length) {
                    return null;
                }

                try {
                    const startPosition = this.position;
                    const tag = this.readVarint();
                    const fieldNumber = tag >>> 3;
                    const wireType = tag & 0x7;

                    // 验证字段编号和线类型的合理性
                    if (fieldNumber === 0) {
                        throw new Error(`无效的字段编号: ${fieldNumber} (位置: ${startPosition})`);
                    }

                    if (wireType > 5 || wireType === 3 || wireType === 4) {
                        throw new Error(`不支持的线类型: ${wireType} (位置: ${startPosition})`);
                    }

                    const field = {
                        number: fieldNumber,
                        wireType: wireType,
                        wireTypeName: WIRE_TYPES[wireType] || 'UNKNOWN',
                        rawValue: null,
                        interpretedValue: null,
                        possibleTypes: FIELD_TYPES[WIRE_TYPES[wireType]] || [],
                        parentPath: parentPath
                    };

                    // 检查是否有用户自定义的类型，使用完整路径
                    const overrideKey = parentPath ? `${parentPath}.${fieldNumber}_${wireType}` : `${fieldNumber}_${wireType}`;
                    const userType = this.fieldOverrides.get(overrideKey);

                    try {
                        const valuePosition = this.position;

                        switch (wireType) {
                            case 0: // VARINT
                                field.rawValue = this.readVarint();
                                field.interpretedValue = this.interpretVarint(field.rawValue, userType);
                                break;

                            case 1: // FIXED64
                                field.rawValue = this.readFixed64();
                                field.interpretedValue = this.interpretFixed64(field.rawValue, userType);
                                break;

                            case 2: // LENGTH_DELIMITED
                                field.rawValue = this.readLengthDelimited();
                                const currentPath = parentPath ? `${parentPath}.${fieldNumber}` : `${fieldNumber}`;
                                field.interpretedValue = this.interpretLengthDelimited(field.rawValue, userType, currentPath);
                                break;

                            case 5: // FIXED32
                                field.rawValue = this.readFixed32();
                                field.interpretedValue = this.interpretFixed32(field.rawValue, userType);
                                break;

                            default:
                                field.interpretedValue = { type: 'unknown', value: `不支持的线类型: ${wireType}` };
                        }
                    } catch (e) {
                        field.interpretedValue = {
                            type: 'error',
                            value: `解析值时出错: ${e.message} (值位置: ${valuePosition})`
                        };
                    }

                    // 添加字节范围信息
                    const endPosition = this.position;
                    field.byteRange = {
                        start: startPosition,
                        end: endPosition - 1,
                        length: endPosition - startPosition
                    };

                    return field;

                } catch (e) {
                    throw new Error(`读取字段时出错: ${e.message}`);
                }
            }

            // 解释 VARINT 值
            interpretVarint(value, userType) {
                const interpretations = [];

                if (userType) {
                    switch (userType) {
                        case 'bool':
                            return { type: 'bool', value: value !== 0 };
                        case 'int32':
                            return { type: 'int32', value: value | 0 };
                        case 'uint32':
                            return { type: 'uint32', value: value >>> 0 };
                        case 'sint32':
                            return { type: 'sint32', value: (value >>> 1) ^ -(value & 1) };
                        case 'int64':
                        case 'uint64':
                            return { type: userType, value: value };
                        case 'enum':
                            return { type: 'enum', value: value };
                    }
                }

                // 自动推断最可能的类型 - 数值类型优先
                console.log(`🔢 interpretVarint: 值=${value}, 自动推断类型优先级`);

                // 1. 数值类型优先（最高优先级）
                interpretations.push({ type: 'uint32', value: value >>> 0 });
                interpretations.push({ type: 'int32', value: value | 0 });

                // 2. 枚举类型（中等优先级）
                if (value < 1000000) {
                    interpretations.push({ type: 'enum', value: value });
                }

                // 3. 布尔类型（最低优先级，仅对0和1）
                if (value === 0 || value === 1) {
                    interpretations.push({ type: 'bool', value: value !== 0 });
                }

                const selectedType = interpretations[0] || { type: 'uint32', value: value };
                console.log(`✅ 选择类型: ${selectedType.type}, 值: ${selectedType.value}`);
                return selectedType;
            }

            // 解释 FIXED32 值
            interpretFixed32(value, userType) {
                if (userType) {
                    switch (userType) {
                        case 'float':
                            const floatView = new Float32Array(new Uint32Array([value]).buffer);
                            return { type: 'float', value: floatView[0] };
                        case 'fixed32':
                            return { type: 'fixed32', value: value };
                        case 'sfixed32':
                            return { type: 'sfixed32', value: value | 0 };
                    }
                }

                // 自动推断类型 - 整型优先于浮点型
                console.log(`🔢 interpretFixed32: 值=${value}, 整型优先解析`);

                // 1. 整型类型优先（最高优先级）
                // 直接返回 fixed32 类型，因为它是最常见的整型表示
                console.log(`✅ 选择整型: fixed32, 值: ${value}`);
                return { type: 'fixed32', value: value };

                // 注释：浮点数解析已降为最低优先级，只有在用户明确指定时才使用
                // const floatView = new Float32Array(new Uint32Array([value]).buffer);
                // const floatValue = floatView[0];
                // if (isFinite(floatValue) && !isNaN(floatValue)) {
                //     return { type: 'float', value: floatValue };
                // }
            }

            // 解释 FIXED64 值
            interpretFixed64(value, userType) {
                if (userType) {
                    switch (userType) {
                        case 'double':
                            // 简化的双精度浮点数解析
                            return { type: 'double', value: `double(${value.toString()})` };
                        case 'fixed64':
                            return { type: 'fixed64', value: value.toString() };
                        case 'sfixed64':
                            return { type: 'sfixed64', value: value.toString() };
                    }
                }

                return { type: 'fixed64', value: value.toString() };
            }

            // 解释长度分隔的数据
            interpretLengthDelimited(bytes, userType, currentPath = '') {
                if (!bytes || bytes.length === 0) {
                    return { type: 'empty', value: '[空数据]' };
                }

                console.log(`🔍 interpretLengthDelimited: 长度=${bytes.length}, 用户类型=${userType}, 路径=${currentPath}`);

                // 如果用户指定了类型，直接使用
                if (userType) {
                    console.log(`✅ 使用用户指定类型: ${userType}`);
                    switch (userType) {
                        case 'string':
                            const str = this.tryParseString(bytes);
                            return { type: 'string', value: str || '[无效的UTF-8字符串]' };
                        case 'bytes':
                            return { type: 'bytes', value: Array.from(bytes).map(b => b.toString(16).padStart(2, '0')).join(' ') };
                        case 'message':
                            const msg = this.tryParseMessage(bytes, currentPath);
                            return { type: 'message', value: msg || '[无法解析为消息]' };
                        case 'repeated':
                            return this.tryParseRepeated(bytes);
                    }
                }

                // 自动推断类型 - 正确的优先级逻辑
                console.log(`🤖 开始自动类型推断，数据长度: ${bytes.length}`);

                // 1. 字符串检测（最高优先级 - 基本类型）
                const stringValue = this.tryParseString(bytes);
                if (stringValue !== null) {
                    console.log(`🔤 尝试字符串解析: "${stringValue.substring(0, 100)}${stringValue.length > 100 ? '...' : ''}" (长度: ${stringValue.length})`);

                    // 字符串判断条件：支持UTF-8字符
                    const isPrintableAscii = /^[\x20-\x7E\s\r\n\t]*$/.test(stringValue);
                    const hasContent = stringValue.trim().length > 0;

                    // 检查是否为有效的文本字符串（包括UTF-8）
                    const hasControlChars = /[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/.test(stringValue);
                    const isValidText = !hasControlChars && hasContent;

                    console.log(`🔍 字符串检查详情:`, {
                        length: stringValue.length,
                        isPrintableAscii: isPrintableAscii,
                        hasContent: hasContent,
                        hasControlChars: hasControlChars,
                        isValidText: isValidText,
                        firstChars: stringValue.substring(0, 20),
                        lastChars: stringValue.length > 20 ? stringValue.substring(stringValue.length - 20) : ''
                    });

                    // 修改判断条件：支持UTF-8字符，只要不包含控制字符就认为是字符串
                    if (isValidText) {
                        console.log(`✅ 识别为字符串: "${stringValue.substring(0, 50)}${stringValue.length > 50 ? '...' : ''}"`);
                        return { type: 'string', value: stringValue };
                    } else {
                        console.log(`❌ 字符串检查失败: validText=${isValidText}, hasControlChars=${hasControlChars}, content=${hasContent}`);

                        if (hasControlChars) {
                            const controlChars = stringValue.match(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g);
                            console.log(`🔍 包含控制字符:`, controlChars ? controlChars.slice(0, 10).map(c => `\\x${c.charCodeAt(0).toString(16).padStart(2, '0')}`) : 'none');
                        }
                    }
                }

                // 2. 消息检测（中等优先级）
                const messageValue = this.tryParseMessage(bytes, currentPath);
                if (messageValue && messageValue.length > 0) {
                    console.log(`📦 尝试消息解析: ${messageValue.length} 个字段`);

                    // 消息判断条件：有合理的字段
                    const hasReasonableFields = messageValue.every(f => f.number > 0 && f.number < 10000);
                    const hasValidStructure = bytes.length >= 2; // 放宽：至少需要tag+value，如08 01

                    if (hasReasonableFields && hasValidStructure) {
                        console.log(`✅ 识别为消息: ${messageValue.length} 个字段`);
                        return { type: 'message', value: messageValue };
                    } else {
                        console.log(`❌ 消息检查失败: reasonable=${hasReasonableFields}, structure=${hasValidStructure}`);
                    }
                }

                // 3. 重复字段检测
                const repeatedValue = this.tryParseRepeated(bytes);
                if (repeatedValue.type !== 'bytes') {
                    console.log(`✅ 识别为重复字段: ${repeatedValue.type}`);
                    return repeatedValue;
                }

                // 4. 兜底：字节数组（最低优先级）
                console.log(`🔧 兜底识别为字节数组`);
                return {
                    type: 'bytes',
                    value: Array.from(bytes).map(b => b.toString(16).padStart(2, '0')).join(' ')
                };
            }

            // 尝试解析重复字段
            tryParseRepeated(bytes) {
                // 如果数据太短，不太可能是重复字段
                if (!bytes || bytes.length < 4) {
                    return { type: 'bytes', value: Array.from(bytes).map(b => b.toString(16).padStart(2, '0')).join(' ') };
                }

                try {
                    const originalData = this.data;
                    const originalPosition = this.position;

                    this.data = bytes;
                    this.position = 0;

                    const values = [];
                    let totalBytesRead = 0;

                    // 尝试解析为packed varint数组
                    while (this.position < this.data.length) {
                        const startPos = this.position;
                        try {
                            const value = this.readVarint();
                            values.push(value);
                            totalBytesRead += (this.position - startPos);

                            // 如果单个varint占用了太多字节，可能不是重复字段
                            if (this.position - startPos > 5) {
                                break;
                            }
                        } catch (e) {
                            break;
                        }
                    }

                    // 恢复状态
                    this.data = originalData;
                    this.position = originalPosition;

                    // 更严格的重复字段判断条件
                    const fullyParsed = totalBytesRead === bytes.length;
                    const hasMultipleValues = values.length > 1;
                    const reasonableValues = values.every(v => v >= 0 && v < 1000000); // 合理的数值范围
                    const notLikeString = !this.tryParseString(bytes); // 确保不是字符串

                    if (hasMultipleValues && fullyParsed && reasonableValues && notLikeString) {
                        console.log(`识别为重复varint字段: ${values.length}个值`);
                        return { type: 'repeated_varint', value: values };
                    }

                    return { type: 'bytes', value: Array.from(bytes).map(b => b.toString(16).padStart(2, '0')).join(' ') };

                } catch (e) {
                    return { type: 'bytes', value: Array.from(bytes).map(b => b.toString(16).padStart(2, '0')).join(' ') };
                }
            }

            // 解码主函数
            decode(input, inputType) {
                try {
                    // 数据转换
                    this.data = inputType === 'hex' ? this.hexToBytes(input) : this.base64ToBytes(input);
                    this.position = 0;

                    console.log(`开始解码 ${inputType} 数据，长度: ${this.data.length} 字节`);
                    console.log('数据预览:', Array.from(this.data.slice(0, Math.min(20, this.data.length)))
                        .map(b => b.toString(16).padStart(2, '0')).join(' '));

                    const fields = [];
                    let fieldCount = 0;

                    while (this.position < this.data.length && fieldCount < 1000) { // 防止无限循环
                        try {
                            const currentPos = this.position;
                            const field = this.readField(''); // 根级字段，路径为空

                            if (field) {
                                fields.push(field);
                                fieldCount++;
                                console.log(`解析字段 ${fieldCount}: 编号=${field.number}, 类型=${field.wireTypeName}, 位置=${currentPos}-${this.position}`);
                            } else {
                                console.warn(`在位置 ${this.position} 无法解析字段`);
                                break;
                            }

                            // 防止位置没有前进
                            if (this.position === currentPos) {
                                console.error(`位置没有前进，可能存在解析错误，位置: ${this.position}`);
                                break;
                            }

                        } catch (fieldError) {
                            console.error(`解析字段时出错 (位置 ${this.position}):`, fieldError.message);
                            // 尝试跳过当前字节继续解析
                            this.position++;
                            if (this.position >= this.data.length) {
                                break;
                            }
                        }
                    }

                    if (fieldCount >= 1000) {
                        console.warn('达到最大字段数量限制，停止解析');
                    }

                    console.log(`解码完成，共解析 ${fields.length} 个字段`);

                    return {
                        success: true,
                        fields: fields,
                        stats: {
                            fieldCount: fields.length,
                            dataLength: this.data.length
                        }
                    };
                } catch (error) {
                    console.error('解码失败:', error);
                    return {
                        success: false,
                        error: `${error.message} (位置: ${this.position || 0})`
                    };
                }
            }

            // 设置字段类型覆盖
            setFieldTypeOverride(fieldNumber, wireType, type, parentPath = '') {
                const key = parentPath ? `${parentPath}.${fieldNumber}_${wireType}` : `${fieldNumber}_${wireType}`;
                this.fieldOverrides.set(key, type);
                console.log(`📝 设置类型覆盖: ${key} -> ${type}`);
                console.log(`📋 当前所有覆盖:`, Array.from(this.fieldOverrides.entries()));
            }
        }

        // UI 控制器
        class UIController {
            constructor() {
                this.decoder = new ProtobufDecoder();
                this.currentInputType = 'hex';
                this.initializeEventListeners();
            }

            initializeEventListeners() {
                // 输入类型切换
                document.querySelectorAll('.format-tab').forEach(tab => {
                    tab.addEventListener('click', (e) => {
                        document.querySelectorAll('.format-tab').forEach(t => t.classList.remove('active'));
                        e.target.classList.add('active');
                        this.currentInputType = e.target.dataset.type;
                        this.updateInputPlaceholder();
                    });
                });

                // 解码按钮
                document.getElementById('decodeBtn').addEventListener('click', () => {
                    this.performDecode();
                });

                // 输入框回车键
                document.getElementById('inputData').addEventListener('keydown', (e) => {
                    if (e.ctrlKey && e.key === 'Enter') {
                        this.performDecode();
                    }
                });

                // 点击其他地方关闭类型选择器
                document.addEventListener('click', (e) => {
                    if (!e.target.closest('.type-selector') && !e.target.closest('.field-type')) {
                        this.closeTypeSelector();
                    }
                });

                // 折叠/展开控制按钮
                document.getElementById('expandAllBtn')?.addEventListener('click', () => {
                    this.expandAll();
                });

                document.getElementById('collapseAllBtn')?.addEventListener('click', () => {
                    this.collapseAll();
                });

                // 目录树切换按钮
                document.getElementById('toggleTocBtn')?.addEventListener('click', () => {
                    this.toggleToc();
                });

                // 目录树关闭按钮
                document.getElementById('tocCloseBtn')?.addEventListener('click', () => {
                    this.hideToc();
                });
            }

            updateInputPlaceholder() {
                const textarea = document.getElementById('inputData');
                if (this.currentInputType === 'hex') {
                    textarea.placeholder = '请输入 Hex 格式的数据，例如：08961a12047465737418c801';
                } else {
                    textarea.placeholder = '请输入 Base64 格式的数据，例如：CJYaEgR0ZXN0GMgB';
                }
            }

            showMessage(message, type = 'error') {
                const container = document.getElementById('messageContainer');
                console.log(`💬 showMessage: ${type} - ${message}, container:`, !!container);

                if (container) {
                    container.innerHTML = `<div class="${type}-message">${message}</div>`;
                    setTimeout(() => {
                        if (container) container.innerHTML = '';
                    }, 5000);
                } else {
                    console.warn('⚠️ messageContainer 元素未找到');
                }
            }

            showLoading(show) {
                const loading = document.getElementById('loadingIndicator');
                const decodeBtn = document.getElementById('decodeBtn');

                console.log(`🔄 showLoading: ${show}, loading元素:`, loading, 'decodeBtn元素:', decodeBtn);

                if (show) {
                    // 确保在结果页面显示loading
                    if (!loading) {
                        console.log('⚠️ loading元素不存在，可能不在结果页面，先切换页面');
                        this.switchTab('result');
                        // 重新获取loading元素
                        const newLoading = document.getElementById('loadingIndicator');
                        if (newLoading) newLoading.style.display = 'flex';
                    } else {
                        loading.style.display = 'flex';
                    }

                    if (decodeBtn) {
                        decodeBtn.disabled = true;
                        decodeBtn.textContent = '解码中...';
                    }
                } else {
                    if (loading) loading.style.display = 'none';
                    if (decodeBtn) {
                        decodeBtn.disabled = false;
                        decodeBtn.textContent = '🚀 开始解码';
                    }
                }
            }

            updateStats(stats, decodeTime) {
                const fieldCount = document.getElementById('fieldCount');
                const dataLength = document.getElementById('dataLength');
                const decodeTimeEl = document.getElementById('decodeTime');
                const statsContainer = document.getElementById('statsContainer');

                console.log(`📊 updateStats: 统计元素检查`, {
                    fieldCount: !!fieldCount,
                    dataLength: !!dataLength,
                    decodeTimeEl: !!decodeTimeEl,
                    statsContainer: !!statsContainer
                });

                if (fieldCount) fieldCount.textContent = stats.fieldCount;
                if (dataLength) dataLength.textContent = stats.dataLength + ' bytes';
                if (decodeTimeEl) decodeTimeEl.textContent = decodeTime + 'ms';
                if (statsContainer) statsContainer.style.display = 'flex';
            }

            resetDecoder(preserveOverrides = false) {
                console.log(`🔄 重置解码器状态, 保留覆盖: ${preserveOverrides}`);

                // 只有在不需要保留覆盖时才清空字段类型覆盖
                if (!preserveOverrides) {
                    this.decoder.fieldOverrides.clear();
                    console.log('🗑️ 已清空字段类型覆盖');
                } else {
                    console.log('💾 保留现有字段类型覆盖');
                }

                // 清空之前的消息显示
                const messageContainer = document.getElementById('messageContainer');
                if (messageContainer) messageContainer.innerHTML = '';

                // 隐藏统计信息
                const statsContainer = document.getElementById('statsContainer');
                if (statsContainer) statsContainer.style.display = 'none';
            }

            performDecode() {
                const input = document.getElementById('inputData').value.trim();

                if (!input) {
                    this.showMessage('请输入要解码的数据');
                    return;
                }

                // 重置解码器状态，避免受之前结果影响
                this.resetDecoder();
                this.showLoading(true);

                // 使用 setTimeout 来模拟异步处理，避免阻塞 UI
                setTimeout(() => {
                    const startTime = performance.now();
                    const result = this.decoder.decode(input, this.currentInputType);
                    const endTime = performance.now();
                    const decodeTime = Math.round(endTime - startTime);

                    this.showLoading(false);

                    if (result.success) {
                        this.displayResult(result.fields);
                        this.updateStats(result.stats, decodeTime);
                        this.showMessage('解码成功！', 'success');
                        // 自动切换到解码结果TAB页
                        this.switchTab('result');
                    } else {
                        this.showMessage(`解码失败: ${result.error}`);
                        document.getElementById('resultContainer').innerHTML =
                            '<div style="text-align: center; color: #e74c3c; padding: 50px;">解码失败，请检查输入数据格式</div>';
                    }
                }, 100);
            }

            performDecodeWithOverrides() {
                const input = document.getElementById('inputData').value.trim();

                if (!input) {
                    this.showMessage('请输入要解码的数据');
                    return;
                }

                // 重置解码器状态，但保留字段类型覆盖
                this.resetDecoder(true);
                this.showLoading(true);

                // 使用 setTimeout 来模拟异步处理，避免阻塞 UI
                setTimeout(() => {
                    const startTime = performance.now();
                    const result = this.decoder.decode(input, this.currentInputType);
                    const endTime = performance.now();
                    const decodeTime = Math.round(endTime - startTime);

                    this.showLoading(false);

                    if (result.success) {
                        this.displayResult(result.fields);
                        this.updateStats(result.stats, decodeTime);
                        this.showMessage('类型变更成功，重新解码完成！', 'success');
                        // 已经在结果页面，不需要再切换
                    } else {
                        this.showMessage(`重新解码失败: ${result.error}`);
                        document.getElementById('resultContainer').innerHTML =
                            '<div style="text-align: center; color: #e74c3c; padding: 50px;">重新解码失败，请检查输入数据格式</div>';
                    }
                }, 100);
            }

            displayResult(fields) {
                const container = document.getElementById('resultContainer');
                const resultControls = document.getElementById('resultControls');
                container.innerHTML = '';

                if (fields.length === 0) {
                    container.innerHTML = '<div style="text-align: center; color: #7f8c8d; padding: 50px;">没有找到有效的字段</div>';
                    if (resultControls) resultControls.style.display = 'none';
                    return;
                }

                // 显示控制按钮
                if (resultControls) resultControls.style.display = 'flex';

                fields.forEach(field => {
                    const fieldElement = this.createFieldElement(field);
                    container.appendChild(fieldElement);
                });

                // 生成目录树
                this.generateToc(fields);

                console.log(`📊 显示解码结果: ${fields.length} 个字段`);
            }

            createFieldElement(field, level = 0, parentPath = '') {
                const fieldDiv = document.createElement('div');
                fieldDiv.className = 'proto-field';
                fieldDiv.style.marginLeft = (level * 20) + 'px';

                // 生成唯一的字段ID用于锚点
                const fieldPath = parentPath ? `${parentPath}.${field.number}` : `${field.number}`;
                const fieldId = `field-${fieldPath}-${field.wireType}`;
                fieldDiv.id = fieldId;

                const headerDiv = document.createElement('div');
                headerDiv.className = 'field-header';

                // 字段编号
                const numberSpan = document.createElement('span');
                numberSpan.className = 'field-number';
                numberSpan.textContent = field.number;

                // 字段类型
                const typeSpan = document.createElement('span');
                typeSpan.className = 'field-type';
                typeSpan.textContent = field.interpretedValue.type;
                typeSpan.title = `点击修改类型 (线类型: ${field.wireTypeName})`;
                typeSpan.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.showTypeSelector(e.target, field);
                });

                // 字节范围信息
                const rangeSpan = document.createElement('span');
                rangeSpan.className = 'byte-range';
                if (field.byteRange) {
                    rangeSpan.textContent = `bytes ${field.byteRange.start}-${field.byteRange.end} (${field.byteRange.length})`;
                    rangeSpan.title = `字节范围: ${field.byteRange.start} 到 ${field.byteRange.end}, 长度: ${field.byteRange.length} 字节`;
                }

                headerDiv.appendChild(numberSpan);
                headerDiv.appendChild(typeSpan);
                headerDiv.appendChild(rangeSpan);

                // 为字符串类型添加复制按钮
                if (field.interpretedValue.type === 'string') {
                    const copyBtn = document.createElement('span');
                    copyBtn.className = 'copy-btn';
                    copyBtn.textContent = '📋';
                    copyBtn.title = '复制原始字符串';
                    copyBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.copyStringValue(field.interpretedValue.value, copyBtn);
                    });
                    headerDiv.appendChild(copyBtn);
                }

                // 值显示
                const valueDiv = document.createElement('div');
                valueDiv.className = 'field-value';

                if (field.interpretedValue.type === 'message' && Array.isArray(field.interpretedValue.value)) {
                    // 可折叠的消息类型
                    const toggleSpan = document.createElement('span');
                    toggleSpan.className = 'field-toggle';
                    toggleSpan.textContent = '−';
                    headerDiv.insertBefore(toggleSpan, numberSpan);

                    const childrenDiv = document.createElement('div');
                    childrenDiv.className = 'field-children';

                    field.interpretedValue.value.forEach(childField => {
                        const childElement = this.createFieldElement(childField, level + 1, fieldPath);
                        childrenDiv.appendChild(childElement);
                    });

                    valueDiv.innerHTML = `<strong>Message</strong> (${field.interpretedValue.value.length} fields)`;

                    // 折叠/展开功能
                    headerDiv.addEventListener('click', () => {
                        const isCollapsed = childrenDiv.classList.contains('collapsed');
                        if (isCollapsed) {
                            childrenDiv.classList.remove('collapsed');
                            toggleSpan.textContent = '−';
                        } else {
                            childrenDiv.classList.add('collapsed');
                            toggleSpan.textContent = '+';
                        }
                    });

                    fieldDiv.appendChild(headerDiv);
                    fieldDiv.appendChild(valueDiv);
                    fieldDiv.appendChild(childrenDiv);
                } else if (field.interpretedValue.type === 'repeated_varint' && Array.isArray(field.interpretedValue.value)) {
                    // 重复字段显示
                    const toggleSpan = document.createElement('span');
                    toggleSpan.className = 'field-toggle';
                    toggleSpan.textContent = '−';
                    headerDiv.insertBefore(toggleSpan, numberSpan);

                    const childrenDiv = document.createElement('div');
                    childrenDiv.className = 'field-children';

                    field.interpretedValue.value.forEach((val, index) => {
                        const itemDiv = document.createElement('div');
                        itemDiv.className = 'proto-field';
                        itemDiv.innerHTML = `<span style="color: #7f8c8d;">[${index}]</span> ${val}`;
                        childrenDiv.appendChild(itemDiv);
                    });

                    valueDiv.innerHTML = `<strong>Repeated</strong> (${field.interpretedValue.value.length} items)`;

                    // 折叠/展开功能
                    headerDiv.addEventListener('click', () => {
                        const isCollapsed = childrenDiv.classList.contains('collapsed');
                        if (isCollapsed) {
                            childrenDiv.classList.remove('collapsed');
                            toggleSpan.textContent = '−';
                        } else {
                            childrenDiv.classList.add('collapsed');
                            toggleSpan.textContent = '+';
                        }
                    });

                    fieldDiv.appendChild(headerDiv);
                    fieldDiv.appendChild(valueDiv);
                    fieldDiv.appendChild(childrenDiv);
                } else {
                    // 普通字段 - 完整显示内容，不截断
                    let displayValue = field.interpretedValue.value;

                    // 为不同类型添加特殊格式
                    if (field.interpretedValue.type === 'string') {
                        // 字符串不加引号，显示转义字符
                        const escapedString = this.escapeStringForDisplay(displayValue);
                        valueDiv.innerHTML = `<span style="color: #27ae60;">${escapedString}</span>`;
                    } else if (field.interpretedValue.type === 'bytes') {
                        valueDiv.innerHTML = `<code style="background: #ffffff; padding: 4px 8px; border-radius: 3px; border: 1px solid #dee2e6;">${displayValue}</code>`;
                    } else if (field.interpretedValue.type === 'error') {
                        valueDiv.innerHTML = `<span style="color: #e74c3c;">❌ ${this.escapeHtml(displayValue)}</span>`;
                    } else {
                        valueDiv.innerHTML = `<span style="color: #495057; font-weight: 500;">${displayValue}</span>`;
                    }

                    fieldDiv.appendChild(headerDiv);
                    fieldDiv.appendChild(valueDiv);
                }

                return fieldDiv;
            }

            // 生成目录树
            generateToc(fields) {
                const tocContent = document.getElementById('tocContent');
                if (!tocContent) return;

                tocContent.innerHTML = '';

                const tocTree = this.buildTocTree(fields);
                tocContent.appendChild(tocTree);
            }

            // 构建目录树结构
            buildTocTree(fields, parentPath = '') {
                const container = document.createElement('div');

                fields.forEach(field => {
                    const fieldPath = parentPath ? `${parentPath}.${field.number}` : `${field.number}`;
                    const fieldId = `field-${fieldPath}-${field.wireType}`;

                    const tocItem = document.createElement('div');
                    tocItem.className = 'toc-item';
                    tocItem.dataset.fieldId = fieldId;

                    // 字段编号
                    const numberSpan = document.createElement('span');
                    numberSpan.className = 'toc-field-number';
                    numberSpan.textContent = `[${field.number}]`;

                    // 字段类型
                    const typeSpan = document.createElement('span');
                    typeSpan.className = 'toc-field-type';
                    typeSpan.textContent = field.interpretedValue.type;

                    tocItem.appendChild(numberSpan);
                    tocItem.appendChild(typeSpan);

                    // 点击跳转到对应字段
                    tocItem.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.scrollToField(fieldId);

                        // 更新活动状态
                        document.querySelectorAll('.toc-item').forEach(item => {
                            item.classList.remove('active');
                        });
                        tocItem.classList.add('active');
                    });

                    container.appendChild(tocItem);

                    // 处理嵌套字段
                    if (field.interpretedValue.type === 'message' &&
                        Array.isArray(field.interpretedValue.value) &&
                        field.interpretedValue.value.length > 0) {

                        const nestedContainer = document.createElement('div');
                        nestedContainer.className = 'toc-nested';

                        const nestedTree = this.buildTocTree(field.interpretedValue.value, fieldPath);
                        nestedContainer.appendChild(nestedTree);

                        container.appendChild(nestedContainer);
                    }
                });

                return container;
            }

            // 滚动到指定字段
            scrollToField(fieldId) {
                const fieldElement = document.getElementById(fieldId);
                const resultContainer = document.getElementById('resultContainer');

                if (fieldElement && resultContainer) {
                    // 计算字段在容器中的位置
                    const containerRect = resultContainer.getBoundingClientRect();
                    const fieldRect = fieldElement.getBoundingClientRect();

                    // 计算需要滚动的距离
                    const scrollTop = resultContainer.scrollTop;
                    const targetScrollTop = scrollTop + fieldRect.top - containerRect.top - (containerRect.height / 2) + (fieldRect.height / 2);

                    // 平滑滚动到目标位置
                    resultContainer.scrollTo({
                        top: targetScrollTop,
                        behavior: 'smooth'
                    });

                    // 高亮显示目标字段
                    fieldElement.style.backgroundColor = '#fff3cd';
                    fieldElement.style.transition = 'background-color 0.3s ease';
                    setTimeout(() => {
                        fieldElement.style.backgroundColor = '';
                        setTimeout(() => {
                            fieldElement.style.transition = '';
                        }, 300);
                    }, 2000);
                }
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            escapeStringForDisplay(text) {
                // 进行HTML转义，但保持换行符等格式字符的原始效果
                let escaped = this.escapeHtml(text);

                // 将换行符转换为HTML的<br>标签，让它们显示为实际换行
                escaped = escaped
                    .replace(/\n/g, '<br>')  // 换行符显示为实际换行
                    .replace(/\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;'); // 制表符显示为4个空格

                return escaped;
            }

            escapeForAttribute(text) {
                // 为HTML属性转义字符串，避免破坏onclick属性
                return text
                    .replace(/\\/g, '\\\\')
                    .replace(/'/g, "\\'")
                    .replace(/"/g, '\\"')
                    .replace(/\n/g, '\\n')
                    .replace(/\r/g, '\\r')
                    .replace(/\t/g, '\\t');
            }

            copyStringValue(originalValue, buttonElement) {
                // 复制原始字符串值到剪贴板
                console.log('📋 复制字符串值:', originalValue);

                // 直接复制原始字符串值（不需要解码，因为传入的就是原始值）
                navigator.clipboard.writeText(originalValue).then(() => {
                    console.log('✅ 字符串已复制到剪贴板');
                    this.showCopySuccess(buttonElement);
                }).catch(err => {
                    console.error('❌ 复制失败:', err);
                    // 降级方案：使用传统的复制方法
                    this.fallbackCopy(originalValue, buttonElement);
                });
            }

            showCopySuccess(buttonElement) {
                // 显示复制成功的反馈（只影响特定按钮）
                if (buttonElement) {
                    buttonElement.classList.add('copied');
                    buttonElement.textContent = '✓';
                    setTimeout(() => {
                        buttonElement.classList.remove('copied');
                        buttonElement.textContent = '📋';
                    }, 1500);
                }
            }

            fallbackCopy(text, buttonElement) {
                // 降级复制方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.opacity = '0';
                document.body.appendChild(textArea);
                textArea.select();

                try {
                    document.execCommand('copy');
                    console.log('✅ 使用降级方案复制成功');
                    this.showCopySuccess(buttonElement);
                } catch (err) {
                    console.error('❌ 降级复制也失败:', err);
                    this.showMessage('复制失败，请手动选择文本复制', 'error');
                } finally {
                    document.body.removeChild(textArea);
                }
            }

            switchTab(tabName) {
                console.log(`🔄 切换到TAB页: ${tabName}`);

                // 更新TAB按钮状态
                const inputTabBtn = document.getElementById('inputTab');
                const resultTabBtn = document.getElementById('resultTab');
                const inputTabContent = document.getElementById('inputTabContent');
                const resultTabContent = document.getElementById('resultTabContent');

                if (tabName === 'input') {
                    inputTabBtn.classList.add('active');
                    resultTabBtn.classList.remove('active');
                    inputTabContent.classList.add('active');
                    resultTabContent.classList.remove('active');
                } else if (tabName === 'result') {
                    inputTabBtn.classList.remove('active');
                    resultTabBtn.classList.add('active');
                    inputTabContent.classList.remove('active');
                    resultTabContent.classList.add('active');
                }

                console.log(`✅ TAB页切换完成: ${tabName}`);
            }

            showTypeSelector(targetElement, field) {
                console.log(`🎯 showTypeSelector 被调用`, { field: field.number, wireType: field.wireType, currentType: field.interpretedValue.type });

                this.closeTypeSelector();

                const selector = document.createElement('div');
                selector.className = 'type-selector';

                const availableTypes = field.possibleTypes || [];
                console.log(`📋 可用类型列表:`, availableTypes);

                // 确保至少有一些基本类型可选
                if (availableTypes.length === 0) {
                    console.warn(`⚠️ 字段 ${field.number} 没有可用类型，使用默认类型`);
                    // 根据线类型提供默认选项 - 整型优先于浮点型
                    const defaultTypes = {
                        0: ['int32', 'uint32', 'bool'],
                        1: ['fixed64', 'sfixed64', 'double'], // 整型优先
                        2: ['string', 'bytes', 'message'],
                        5: ['fixed32', 'sfixed32', 'float'] // 整型优先
                    };
                    availableTypes.push(...(defaultTypes[field.wireType] || ['bytes']));
                }

                availableTypes.forEach(type => {
                    const option = document.createElement('div');
                    option.className = 'type-option';
                    option.textContent = type;

                    // 高亮当前类型
                    if (type === field.interpretedValue.type) {
                        option.style.backgroundColor = '#e3f2fd';
                        option.style.fontWeight = 'bold';
                    }

                    option.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log(`🎯 类型选项被点击: ${type} for 字段 ${field.number}`);
                        this.changeFieldType(field, type);
                        this.closeTypeSelector();
                    });

                    selector.appendChild(option);
                });

                // 定位选择器
                const rect = targetElement.getBoundingClientRect();
                selector.style.position = 'fixed';
                selector.style.left = rect.left + 'px';
                selector.style.top = (rect.bottom + 5) + 'px';
                selector.style.zIndex = '1000';
                selector.style.backgroundColor = 'white';
                selector.style.border = '1px solid #ddd';
                selector.style.borderRadius = '4px';
                selector.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';

                document.body.appendChild(selector);
                this.currentTypeSelector = selector;

                console.log(`✅ 类型选择器已显示，位置: ${rect.left}, ${rect.bottom + 5}, 选项数: ${availableTypes.length}`);
            }

            closeTypeSelector() {
                if (this.currentTypeSelector) {
                    this.currentTypeSelector.remove();
                    this.currentTypeSelector = null;
                }
            }

            changeFieldType(field, newType) {
                console.log(`🔄 changeFieldType 被调用: 字段${field.number}, 线类型${field.wireType}, 新类型${newType}, 路径=${field.parentPath || 'root'}`);

                // 设置字段类型覆盖，包含路径信息
                this.decoder.setFieldTypeOverride(field.number, field.wireType, newType, field.parentPath);

                console.log(`📝 类型覆盖已设置，开始重新解码...`);

                // 确保在结果页面，然后重新解码
                this.switchTab('result');

                // 重新解码，但保留字段类型覆盖
                this.performDecodeWithOverrides();
            }

            expandAll() {
                console.log('📂 展开所有字段');
                const allCollapsed = document.querySelectorAll('.field-children.collapsed');
                const allToggles = document.querySelectorAll('.field-toggle');

                allCollapsed.forEach(element => {
                    element.classList.remove('collapsed');
                });

                allToggles.forEach(toggle => {
                    toggle.textContent = '−';
                });

                console.log(`✅ 已展开 ${allCollapsed.length} 个折叠项`);
            }

            collapseAll() {
                console.log('📁 折叠所有字段');
                const allExpanded = document.querySelectorAll('.field-children:not(.collapsed)');
                const allToggles = document.querySelectorAll('.field-toggle');

                allExpanded.forEach(element => {
                    element.classList.add('collapsed');
                });

                allToggles.forEach(toggle => {
                    toggle.textContent = '+';
                });

                console.log(`✅ 已折叠 ${allExpanded.length} 个展开项`);
            }

            // 切换目录树显示/隐藏
            toggleToc() {
                const toc = document.getElementById('resultToc');
                const toggleBtn = document.getElementById('toggleTocBtn');

                if (toc.style.display === 'none' || !toc.style.display) {
                    this.showToc();
                } else {
                    this.hideToc();
                }
            }

            // 显示目录树
            showToc() {
                const toc = document.getElementById('resultToc');
                const toggleBtn = document.getElementById('toggleTocBtn');

                if (toc) {
                    toc.style.display = 'block';
                    toggleBtn.title = '隐藏目录';
                    console.log('📑 显示目录树');
                }
            }

            // 隐藏目录树
            hideToc() {
                const toc = document.getElementById('resultToc');
                const toggleBtn = document.getElementById('toggleTocBtn');

                if (toc) {
                    toc.style.display = 'none';
                    toggleBtn.title = '显示目录';
                    console.log('📑 隐藏目录树');
                }
            }
        }

        // 全局变量
        let uiController;

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            uiController = new UIController();
        });
    </script>
</body>
</html>