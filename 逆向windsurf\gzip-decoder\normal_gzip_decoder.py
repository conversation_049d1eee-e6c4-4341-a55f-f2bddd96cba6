import base64
import gzip
import logging
from pathlib import Path
from typing import Optional, Tuple

import binascii

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GzipDecoder:
    """Gzip解码器类"""
    
    GZIP_MAGIC = '1F8B08'  # Gzip文件的魔数
    
    @staticmethod
    def convert_to_hex(data_str: str) -> Optional[str]:
        """
        将输入数据转换为hex格式
        
        Args:
            data_str: 输入的字符串数据
            
        Returns:
            转换后的大写hex字符串，如果转换失败则返回None
        """
        # 移除所有空白字符
        data_str = ''.join(data_str.split())
        
        # 首先检查是否为hex格式
        if all(c in "0123456789ABCDEFabcdef" for c in data_str):
            return data_str.upper()
        
        # 如果不是hex格式，尝试base64解码
        try:
            if all(c in "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=" for c in data_str):
                decoded = base64.b64decode(data_str)
                return binascii.hexlify(decoded).decode('utf-8').upper()
        except Exception as e:
            logger.debug(f"Base64解码失败: {e}")
        
        return None

    @staticmethod
    def format_hex(hex_str: str) -> str:
        """将hex字符串格式化为带空格的形式"""
        return ' '.join(hex_str[i:i+2] for i in range(0, len(hex_str), 2))

    def decode_gzip_data(self, hex_data: str) -> Optional[Tuple[str, str]]:
        """
        解码gzip压缩的数据
        
        Args:
            hex_data: hex格式的输入数据
            
        Returns:
            解压后的(hex格式数据, base64格式数据)元组，解压失败则返回None
        """
        # 找到gzip magic number的起始位置
        gzip_start = hex_data.find(self.GZIP_MAGIC)
        if gzip_start == -1:
            logger.error("未找到gzip数据")
            return None
        
        try:
            # 解压gzip数据
            gzip_data = bytes.fromhex(hex_data[gzip_start:])
            decompressed = gzip.decompress(gzip_data)
            
            # 转换为hex和base64格式
            hex_output = binascii.hexlify(decompressed).decode('utf-8').upper()
            base64_output = base64.b64encode(decompressed).decode('utf-8')
            
            return self.format_hex(hex_output), base64_output
            
        except Exception as e:
            logger.error(f"解压失败: {e}")
            return None

    def decode_file(self, file_path: str, output_dir: str = ".") -> bool:
        """
        解码gzip文件
        
        Args:
            file_path: 输入文件路径
            output_dir: 输出目录路径
            
        Returns:
            解码是否成功
        """
        try:
            # 确保输入文件存在
            input_path = Path(file_path)
            if not input_path.exists():
                logger.error(f"文件不存在: {file_path}")
                return False
            
            # 读取并解码文件
            with open(input_path, 'rb') as f:
                data = f.read()
            data_str = data.decode('utf-8', errors='ignore')
            
            # 转换为hex格式
            hex_data = self.convert_to_hex(data_str)
            if hex_data is None:
                logger.error("无法识别的数据格式")
                return False
            
            # 解压数据
            result = self.decode_gzip_data(hex_data)
            if result is None:
                return False
                
            hex_output, base64_output = result
            
            # 确保输出目录存在
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # 保存结果
            (output_path / "decoded_hex.txt").write_text(hex_output)
            (output_path / "decoded_base64.txt").write_text(base64_output)
            
            # 打印结果
            print(hex_output)
            print(base64_output)
            
            return True
            
        except Exception as e:
            logger.error(f"处理文件时出错: {e}")
            return False

def main():
    """主函数"""
    # import argparse
    #
    # parser = argparse.ArgumentParser(description='Gzip数据解码工具（非Chunked）')
    # parser.add_argument('file', help='要解码的文件路径')
    # parser.add_argument('-o', '--output', default='.', help='输出目录路径')
    # args = parser.parse_args()
    
    decoder = GzipDecoder()
    # success = decoder.decode_file(args.file, args.output)
    success = decoder.decode_file('demo1_req.dump', '.')
    exit(0 if success else 1)

if __name__ == "__main__":
    main() 