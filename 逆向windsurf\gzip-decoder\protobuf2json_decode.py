#!/usr/bin/env python3
from pathlib import Path
from venv import logger

import protod


def decode_protobuf(hex_str):
    """解码 protobuf 二进制数据"""
    try:
        # 将 hex 字符串转换为字节
        binary_data = bytes.fromhex(hex_str)
        # 解码数据
        result = protod.dump(binary_data)
        return result
    except Exception as e:
        return {"error": f"解码失败: {str(e)}"}

def main():

    file_path = "decoded_hex.txt"
    # 确保输入文件存在
    input_path = Path(file_path)
    if not input_path.exists():
        logger.error(f"文件不存在: {file_path}")
        return False

    # 读取并解码文件
    with open(input_path, 'rb') as f:
        data = f.read()
    hex_str = data.decode('utf-8', errors='ignore')
    
    # 解码数据
    decoded_data = decode_protobuf(hex_str)
    
    # 输出结果到控制台
    print(decoded_data)

if __name__ == "__main__":
    main() 