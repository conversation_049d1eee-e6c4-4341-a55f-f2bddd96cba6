import base64
import gzip
import logging
from dataclasses import dataclass
from pathlib import Path
from typing import Optional, List

import binascii

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class ChunkedData:
    """分块数据的数据类"""
    size: int
    data: bytes
    
class ChunkedGzipDecoder:
    """Chunked编码的Gzip解码器类"""
    
    GZIP_MAGIC = '1F8B08'  # Gzip文件的魔数

    @staticmethod
    def convert_to_hex(data_str: str) -> Optional[str]:
        """
        将输入数据转换为hex格式

        Args:
            data_str: 输入的字符串数据

        Returns:
            转换后的大写hex字符串，如果转换失败则返回None
        """
        # 移除所有空白字符
        data_str = ''.join(data_str.split())

        # 首先检查是否为hex格式
        if all(c in "0123456789ABCDEFabcdef" for c in data_str):
            return data_str.upper()

        # 如果不是hex格式，尝试base64解码
        try:
            if all(c in "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=" for c in data_str):
                decoded = base64.b64decode(data_str)
                return binascii.hexlify(decoded).decode('utf-8').upper()
        except Exception as e:
            logger.debug(f"Base64解码失败: {e}")

        return None
    
    @staticmethod
    def parse_chunks(hex_data: str) -> List[ChunkedData]:
        """
        解析出所有的chunks数据块
        原理：chunks数据的格式为 分隔符(3个字节)+chunk size(2个字节)+data(chunk size个字节)
        分隔符有010000、030000两种，030000出现在最后一个chunk，可以直接舍弃

        Args:
            hex_data: hex格式的数据
            
        Returns:
            ChunkedData列表
        """
        chunks = []
        pos = 0
        
        while pos + 8 < len(hex_data):  # 确保至少有3字节分隔符+2字节大小
            # 检查分隔符
            separator = hex_data[pos:pos+6]
            if separator != "010000":
                logger.error(f"无效的分隔符: {separator} at position {pos}")
                break
            
            # 解析chunk大小（2字节）
            chunk_size = int(hex_data[pos+6:pos+10], 16)
            if chunk_size == 0:  # 最后一个chunk
                break
            
            # 提取chunk数据（从GZIP magic number开始）
            data_start = pos + 10  # 跳过分隔符和大小
            chunk_data = bytes.fromhex(hex_data[data_start:data_start+chunk_size*2])
            chunks.append(ChunkedData(chunk_size, chunk_data))
            
            # 移动到下一个chunk
            pos = data_start + chunk_size * 2
            
        return chunks
    
    @staticmethod
    def decode_gzip_chunk(chunk_data: bytes) -> Optional[str]:
        """
        解码单个gzip压缩的数据块
        
        Args:
            chunk_data: 压缩的数据
            
        Returns:
            解压后的(hex格式数据, base64格式数据)元组
        """
        try:
            decompressed = gzip.decompress(chunk_data)
            hex_output = binascii.hexlify(decompressed).decode('utf-8').upper()
            return hex_output
        except Exception as e:
            logger.error(f"解压chunk失败: {e}")
            return None

    def decode_file(self, file_path: str, output_dir: str = ".") -> bool:
        """
        解码chunked编码的gzip文件
        
        Args:
            file_path: 输入文件路径
            output_dir: 输出目录路径
            
        Returns:
            解码是否成功
        """
        try:
            # 读取文件
            with open(file_path, 'rb') as f:
                data_str = f.read().decode('utf-8').upper()

                # 转换为hex格式
                hex_data = self.convert_to_hex(data_str)
                if hex_data is None:
                    logger.error("无法识别的数据格式")
                    return False

            # 解析chunks
            chunks = self.parse_chunks(hex_data)
            
            # 解码每个chunk
            combined_hex = []

            for chunk in chunks:
                hex_output = self.decode_gzip_chunk(chunk.data)
                combined_hex.append(hex_output)

            # 合并结果
            final_hex = ''.join(combined_hex)

            # 保存结果
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            (output_path / "decoded_chunked_hex.txt").write_text(final_hex)

            # 打印结果
            print("Hex output:")
            print(final_hex)

            return True
            
        except Exception as e:
            logger.error(f"处理文件时出错: {e}")
            return False

def main():
    """主函数"""
    decoder = ChunkedGzipDecoder()
    success = decoder.decode_file('demo1_resp.dump', '.')
    exit(0 if success else 1)

if __name__ == "__main__":
    main()