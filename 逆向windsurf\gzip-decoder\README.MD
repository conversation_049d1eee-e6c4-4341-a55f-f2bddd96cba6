## 获取体验会员

1. 登录官网：https://codeium.com/profile
2. 注册登录
3. 使用本地Windsurf客户端登录账户，即可获取14天体验会员


## 获取UID

> `UID`并非是官方定义的名词，而是作者方便理解定义的。

1. 登录官网：https://codeium.com/profile
2. F12开发者工具 - 网络 - 搜索"GetCurrentUser"，没有的话可以刷新一下页面就有了
3. 接口名称上 - 右键 - 复制 - 复制响应
[![pAoIf6x.png](https://s21.ax1x.com/2024/12/04/pAoIf6x.png)](https://imgse.com/i/pAoIf6x)
4. 打开`windsurf_tool.html`网页 - 粘贴响应 - 解析 - 得到`UID`

## 获取模型ID

1. 配置好抓包工具
2. 在windsurf中使用write模式，选择模型，然后输入一些问候语，如：`hi~`、`hi`、`你好~`
3. 抓包工具会抓取到两条请求，第一条是我们发送的消息，第二条是工具调用，其中第一条是我们解包的对象
4. 使用解包工具解包请求，即可获取模型ID

[normal_gzip_decoder.py](normal_gzip_decoder.py)：请求解包
[chunked_gzip_decoder.py](chunked_gzip_decoder.py)：响应解包