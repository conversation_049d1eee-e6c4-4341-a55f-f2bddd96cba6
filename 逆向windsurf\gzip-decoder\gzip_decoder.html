<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gzip解码工具</title>
    <style>
        :root {
            --primary-color: #4CAF50;
            --hover-color: #45a049;
            --bg-color: #f9f9f9;
            --text-color: #333;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background-color: var(--bg-color);
            color: var(--text-color);
            line-height: 1.6;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: var(--primary-color);
        }

        .drop-zone {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .drop-zone:hover, .drop-zone.dragover {
            border-color: var(--primary-color);
            background: #f0f8f0;
        }

        .drop-zone p {
            margin: 0;
            font-size: 1.1em;
            color: #666;
        }

        .output-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: none;
        }

        .outputs-wrapper {
            display: flex;
            gap: 20px;
        }

        .output-section {
            flex: 1;
            margin-bottom: 20px;
        }

        .output-section h3 {
            margin-bottom: 10px;
            color: var(--primary-color);
        }

        .output-content {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-all;
            font-family: monospace;
            height: 300px;
            max-height: 50vh;
        }

        .output-content::-webkit-scrollbar {
            width: 8px;
        }

        .output-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .output-content::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        .output-content::-webkit-scrollbar-thumb:hover {
            background: #666;
        }

        .copy-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            float: right;
            margin-top: 10px;
            transition: background 0.3s ease;
        }

        .copy-btn:hover {
            background: var(--hover-color);
        }

        .error {
            color: #f44336;
            text-align: center;
            margin-top: 10px;
            display: none;
        }

        .input-section {
            margin: 20px 0;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .input-section textarea {
            width: 100%;
            height: 100px;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            resize: vertical;
            font-family: monospace;
            margin-bottom: 10px;
        }

        .decode-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .decode-btn:hover {
            background: var(--hover-color);
        }

        .or-divider {
            text-align: center;
            margin: 20px 0;
            color: #666;
        }

        .input-buttons {
            display: flex;
            gap: 10px;
        }

        .clear-btn {
            background: #f44336;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .clear-btn:hover {
            background: #d32f2f;
        }

        .tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .tab-btn {
            padding: 10px 20px;
            border: none;
            background: #f0f0f0;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
        }

        .tab-btn:hover {
            background: #e0e0e0;
        }

        .tab-btn.active {
            background: var(--primary-color);
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 按钮动画 */
        .decode-btn, .clear-btn, .copy-btn {
            transition: all 0.2s ease;
        }

        .decode-btn:active, .clear-btn:active, .copy-btn:active {
            transform: scale(0.95);
        }

        /* 标签页切换动画 */
        .tab-content {
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
        }

        .tab-content.active {
            opacity: 1;
            transform: translateY(0);
        }

        /* 输出容器显示动画 */
        .output-container {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.4s ease;
        }

        .output-container.show {
            opacity: 1;
            transform: translateY(0);
        }

        /* 错误信息动画 */
        .error {
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        }

        .error.show {
            opacity: 1;
            transform: translateY(0);
        }

        /* 拖放区域动画增强 */
        .drop-zone {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .drop-zone.dragover {
            transform: scale(1.02);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .output-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .compact-switch {
            display: flex;
            align-items: center;
            cursor: pointer;
            user-select: none;
        }

        .compact-switch input {
            margin-right: 5px;
        }

        .switch-text {
            font-size: 0.9em;
            color: #666;
        }
        
        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 100;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .modal.show {
            opacity: 1;
        }
        
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 500px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transform: translateY(-20px);
            transition: transform 0.3s ease;
        }
        
        .modal.show .modal-content {
            transform: translateY(0);
        }
        
        .modal-title {
            font-size: 1.2em;
            color: var(--primary-color);
            margin-bottom: 15px;
        }
        
        .modal-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }
        
        .modal-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .modal-btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .modal-btn-primary:hover {
            background: var(--hover-color);
        }
        
        .modal-btn-secondary {
            background: #e0e0e0;
            color: #333;
        }
        
        .modal-btn-secondary:hover {
            background: #ccc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Gzip解码工具</h1>
        <div class="tabs">
            <button class="tab-btn active" data-tab="request">请求体解压</button>
            <button class="tab-btn" data-tab="response">响应体(Chunk)解压</button>
        </div>

        <div class="tab-content active" id="requestTab">
            <div class="drop-zone" id="dropZone">
                <p>拖拽文件到此处或点击选择文件</p>
                <input type="file" id="fileInput" style="display: none">
            </div>

            <div class="or-divider">- 或者 -</div>
            
            <div class="input-section">
                <textarea id="inputText" placeholder="在此粘贴Hex或Base64格式的数据..."></textarea>
                <div class="input-buttons">
                    <button class="clear-btn" onclick="clearInput()">清空</button>
                    <button class="decode-btn" onclick="processInputText()">解码</button>
                </div>
            </div>

            <div class="error" id="error"></div>

            <div class="output-container" id="outputContainer">
                <div class="outputs-wrapper">
                    <div class="output-section">
                        <div class="output-header">
                            <h3>Hex输出</h3>
                            <label class="compact-switch">
                                <input type="checkbox" onchange="toggleCompactMode(this, 'hexOutput')" checked>
                                <span class="switch-text">紧凑模式</span>
                            </label>
                        </div>
                        <div class="output-content" id="hexOutput"></div>
                        <button class="copy-btn" onclick="copyToClipboard('hexOutput')">复制</button>
                    </div>

                    <div class="output-section">
                        <h3>Base64输出</h3>
                        <div class="output-content" id="base64Output"></div>
                        <button class="copy-btn" onclick="copyToClipboard('base64Output')">复制</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="tab-content" id="responseTab" style="display: none;">
            <div class="drop-zone" id="chunkDropZone">
                <p>拖拽文件到此处或点击选择文件</p>
                <input type="file" id="chunkFileInput" style="display: none">
            </div>

            <div class="or-divider">- 或者 -</div>

            <div class="input-section">
                <textarea id="chunkInputText" placeholder="在此粘贴Hex或Base64格式的数据..."></textarea>
                <div class="input-buttons">
                    <button class="clear-btn" onclick="clearChunkInput()">清空</button>
                    <button class="decode-btn" onclick="processChunkInput()">解码</button>
                </div>
            </div>

            <div class="error" id="chunkError"></div>

            <div class="output-container" id="chunkOutputContainer">
                <div class="outputs-wrapper">
                    <div class="output-section">
                        <div class="output-header">
                            <h3>Hex输出</h3>
                            <label class="compact-switch">
                                <input type="checkbox" onchange="toggleCompactMode(this, 'chunkHexOutput')" checked>
                                <span class="switch-text">紧凑模式</span>
                            </label>
                        </div>
                        <div class="output-content" id="chunkHexOutput"></div>
                        <button class="copy-btn" onclick="copyToClipboard('chunkHexOutput')">复制</button>
                    </div>

                    <div class="output-section">
                        <h3>Base64输出</h3>
                        <div class="output-content" id="chunkBase64Output"></div>
                        <button class="copy-btn" onclick="copyToClipboard('chunkBase64Output')">复制</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 粘贴剪贴板内容确认弹窗 -->
    <div class="modal" id="clipboardModal">
        <div class="modal-content">
            <div class="modal-title">检测到剪贴板内容</div>
            <p>剪贴板内容与当前输入框内容不同，是否粘贴？</p>
            <div class="modal-buttons">
                <button class="modal-btn modal-btn-secondary" onclick="closeModal('clipboardModal')">取消</button>
                <button class="modal-btn modal-btn-primary" onclick="pasteClipboardContent()">粘贴</button>
            </div>
        </div>
    </div>
    
    <!-- 解码成功跳转提示弹窗 -->
    <div class="modal" id="successModal">
        <div class="modal-content">
            <div class="modal-title">解码成功</div>
            <p>选择一个Protobuf解码器进行跳转，Hex格式的解码内容将自动复制。</p>
            <div style="margin-top: 15px;">
                <select id="decoderSelect" style="width: 100%; padding: 8px; border-radius: 4px; border: 1px solid #ccc;">
                </select>
            </div>
            <div class="modal-buttons">
                <button class="modal-btn modal-btn-secondary" onclick="closeModal('successModal')">取消</button>
                <button class="modal-btn modal-btn-primary" onclick="redirectToProtobufDecoder()">跳转</button>
            </div>
        </div>
    </div>

    <script>
        // Protobuf解码器地址列表
        const protobufDecoders = [
            { name: "netlify.app - Protobuf 在线解码器", url: "https://protobuf-decoder.netlify.app/" },
            { name: "************** - Protobuf 在线解码器", url: "http://**************/" },
            { name: "marcgravell.com - Protobuf 在线解码器", url: "https://protogen.marcgravell.com/decode" },
        ];
        
        // Gzip魔常量
        const GZIP_MAGIC = '1F8B08';
        
        // 当前激活的输入框ID
        let activeInputId = 'inputText';

        // 拖放区域相关事件处理
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');
        const error = document.getElementById('error');
        const outputContainer = document.getElementById('outputContainer');

        // 阻止默认拖放行为
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, preventDefaults, false);
            document.body.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        // 拖放效果
        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, unhighlight, false);
        });

        function highlight(e) {
            dropZone.classList.add('dragover');
        }

        function unhighlight(e) {
            dropZone.classList.remove('dragover');
        }

        // 处理文件拖放
        dropZone.addEventListener('drop', handleDrop, false);
        dropZone.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', handleFileSelect);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const file = dt.files[0];
            processFile(file);
        }

        function handleFileSelect(e) {
            const file = e.target.files[0];
            processFile(file);
            e.target.value = '';
        }

        // 文处理
        function processFile(file) {
            const reader = new FileReader();
            reader.onload = async function(e) {
                try {
                    const content = e.target.result;
                    // 将件内容转换为hex格式
                    const hexData = convertToHex(content);
                    if (!hexData) {
                        showError("无法识别的数据格式");
                        return;
                    }

                    // 查找并解码gzip数据
                    const result = await decodeGzipData(hexData);
                    if (!result) {
                        showError("解压失败");
                        return;
                    }

                    // 显示结果
                    displayResults(result.hex, result.base64);
                } catch (err) {
                    showError("处理文件时出错: " + err.message);
                }
            };
            reader.readAsText(file);
        }

        // 转换为hex格式
        function convertToHex(data) {
            // 移除所有空白字符
            data = data.replace(/\s/g, '');
            
            // 检查是否为hex格式
            if (/^[0-9A-Fa-f]+$/.test(data)) {
                return data.toUpperCase();
            }
            
            // 尝试base64解码
            try {
                if (/^[A-Za-z0-9+/=]+$/.test(data)) {
                    const decoded = atob(data);
                    return Array.from(decoded)
                        .map(c => c.charCodeAt(0).toString(16).padStart(2, '0'))
                        .join('')
                        .toUpperCase();
                }
            } catch (e) {
                console.debug("Base64解码失败:", e);
            }
            
            return null;
        }

        // 解码gzip数据
        async function decodeGzipData(hexData) {
            const gzipStart = hexData.indexOf(GZIP_MAGIC);
            if (gzipStart === -1) {
                showError("未找到gzip数据");
                return null;
            }

            try {
                const gzipData = hexToBytes(hexData.slice(gzipStart));
                const decompressed = await decompressGzip(gzipData);
                
                const hex = Array.from(decompressed)
                    .map(b => b.toString(16).padStart(2, '0'))
                    .join(' ')
                    .toUpperCase();
                
                const base64 = arrayBufferToBase64(decompressed);
                
                return { hex, base64 };
            } catch (e) {
                showError("解压失败: " + e.message);
                return null;
            }
        }

        // 添加新的辅助函数用于处理大型数组的 base64 转换
        function arrayBufferToBase64(buffer) {
            const chunks = [];
            const chunkSize = 0x8000; // 每次处理 32KB
            
            for (let i = 0; i < buffer.length; i += chunkSize) {
                const chunk = buffer.slice(i, i + chunkSize);
                chunks.push(String.fromCharCode.apply(null, chunk));
            }
            
            return btoa(chunks.join(''));
        }

        // hex字符串转字节数组
        function hexToBytes(hex) {
            const bytes = new Uint8Array(hex.length / 2);
            for (let i = 0; i < hex.length; i += 2) {
                bytes[i / 2] = parseInt(hex.substr(i, 2), 16);
            }
            return bytes;
        }

        // 解压gzip数据
        async function decompressGzip(data) {
            const ds = new DecompressionStream('gzip');
            const decompressedStream = new Blob([data]).stream().pipeThrough(ds);
            const decompressedBlob = await new Response(decompressedStream).blob();
            const buffer = await decompressedBlob.arrayBuffer();
            return new Uint8Array(buffer);
        }
        
        // 弹窗相关函数
        function showModal(modalId) {
            const modal = document.getElementById(modalId);
            modal.style.display = 'block';
            setTimeout(() => modal.classList.add('show'), 10);
            
            // 如果是成功弹窗，填充解码器下拉列表
            if (modalId === 'successModal') {
                const select = document.getElementById('decoderSelect');
                // 清空现有选项
                select.innerHTML = '';
                // 添加解码器选项
                protobufDecoders.forEach((decoder, index) => {
                    const option = document.createElement('option');
                    option.value = index;
                    option.textContent = decoder.name;
                    select.appendChild(option);
                });
            }
            
            // 设置回车键监听
            document.addEventListener('keydown', function modalEnterHandler(e) {
                if (e.key === 'Enter') {
                    if (modalId === 'clipboardModal') {
                        pasteClipboardContent();
                    } else if (modalId === 'successModal') {
                        redirectToProtobufDecoder();
                    }
                    document.removeEventListener('keydown', modalEnterHandler);
                }
            });
        }
        
        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            modal.classList.remove('show');
            setTimeout(() => modal.style.display = 'none', 300);
        }
        
        // 跳转到Protobuf在线解码器
        function redirectToProtobufDecoder() {
            // 获取当前激活标签页对应的输出内容
            let contentToCopy;
            const activeTab = document.querySelector('.tab-btn.active').getAttribute('data-tab');
            
            if (activeTab === 'request') {
                // 获取Hex输出内容
                contentToCopy = document.getElementById('hexOutput').textContent;
            } else {
                // 获取Chunk Hex输出内容
                contentToCopy = document.getElementById('chunkHexOutput').textContent;
            }
            
            // 获取选择的解码器
            const select = document.getElementById('decoderSelect');
            const selectedIndex = select.value;
            const selectedDecoder = protobufDecoders[selectedIndex];
            
            // 复制解码内容到剪贴板
            navigator.clipboard.writeText(contentToCopy).then(() => {
                // 打开新标签页
                window.open(selectedDecoder.url, '_blank');
                closeModal('successModal');
            });
        }
        
        // 检查剪贴板内容
        async function checkClipboard() {
            try {
                // 获取当前激活的输入框
                const inputElement = document.getElementById(activeInputId);
                if (!inputElement) return;
                
                const clipboardText = await navigator.clipboard.readText();
                
                // 如果剪贴板内容不为空且与当前输入框内容不同
                if (clipboardText && clipboardText.trim() !== inputElement.value.trim()) {
                    // 验证剪贴板内容是否为有效的Hex或Base64格式
                    const isValidFormat = convertToHex(clipboardText.trim()) !== null;
                    if (isValidFormat) {
                        showModal('clipboardModal');
                    } else {
                        console.debug("剪贴板内容不是有效的Hex或Base64格式");
                    }
                }
            } catch (err) {
                console.error('读取剪贴板失败:', err);
            }
        }
        
        // 粘贴剪贴板内容到当前激活的输入框
        async function pasteClipboardContent() {
            try {
                const clipboardText = await navigator.clipboard.readText();
                document.getElementById(activeInputId).value = clipboardText;
                closeModal('clipboardModal');
            } catch (err) {
                console.error('粘贴剪贴板失败:', err);
            }
        }

        // 存储原始的hex数据
        let originalHexData = {};

        function toggleCompactMode(checkbox, outputId) {
            const outputElement = document.getElementById(outputId);
            const hexContent = originalHexData[outputId] || outputElement.textContent;
            
            // 首次切换时保存原始数据
            if (!originalHexData[outputId]) {
                originalHexData[outputId] = hexContent;
            }
            
            if (checkbox.checked) {
                // 紧凑模式：移除所有空格
                outputElement.textContent = hexContent.replace(/\s/g, '');
            } else {
                // 标准模式：每两个字符添加空格
                outputElement.textContent = hexContent;
            }
        }

        // 修改displayResults函数，根据复选框状态决定显示格式
        function displayResults(hex, base64) {
            originalHexData['hexOutput'] = hex;
            const checkbox = document.querySelector('#requestTab .compact-switch input');
            document.getElementById('hexOutput').textContent = checkbox.checked ? 
                hex.replace(/\s/g, '') : hex;
            document.getElementById('base64Output').textContent = base64;
            
            outputContainer.style.display = 'block';
            setTimeout(() => outputContainer.classList.add('show'), 10);
            error.style.display = 'none';
            error.classList.remove('show');
            
            // 解码成功后显示弹窗提示
            showModal('successModal');
        }

        // 显示错误信息
        function showError(message) {
            error.textContent = message;
            error.style.display = 'block';
            setTimeout(() => error.classList.add('show'), 10);
            outputContainer.style.display = 'none';
            outputContainer.classList.remove('show');
        }

        // 复制到剪贴板
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            navigator.clipboard.writeText(text).then(() => {
                const button = element.nextElementSibling;
                const originalText = button.textContent;
                button.textContent = '已复制';
                setTimeout(() => {
                    button.textContent = originalText;
                }, 2000);
            });
        }

        // 在script部分添加处理输入框的函数
        function processInputText() {
            // 更新当前激活的输入框ID
            activeInputId = 'inputText';
            const input = document.getElementById('inputText').value.trim();
            if (!input) {
                showError("请输入数据");
                return;
            }

            try {
                const hexData = convertToHex(input);
                if (!hexData) {
                    showError("无法识别的数据格式");
                    return;
                }

                decodeGzipData(hexData).then(result => {
                    if (result) {
                        displayResults(result.hex, result.base64);
                    }
                    return result !== null; // 返回解码是否成功
                });
            } catch (err) {
                showError("处理数据时出错: " + err.message);
            }
        }

        // 在script部分添加清空函数
        function clearInput() {
            document.getElementById('inputText').value = '';
            error.style.display = 'none';
            outputContainer.style.display = 'none';
        }

        // 标签页切换功能
        document.querySelectorAll('.tab-btn').forEach(button => {
            button.addEventListener('click', () => {
                // 更新当前激活的输入框ID
                if (button.getAttribute('data-tab') === 'request') {
                    activeInputId = 'inputText';
                } else {
                    activeInputId = 'chunkInputText';
                }
                // 移除所有活动状态
                document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                    content.style.display = 'none';
                });
                
                // 添加当前标签的活动状态
                button.classList.add('active');
                const tabId = button.getAttribute('data-tab') + 'Tab';
                const tabContent = document.getElementById(tabId);
                tabContent.style.display = 'block';
                setTimeout(() => tabContent.classList.add('active'), 10);

                // 切换标签页时，根据复选框状态更新输出格式
                const outputId = tabId === 'requestTab' ? 'hexOutput' : 'chunkHexOutput';
                const checkbox = document.querySelector(`#${tabId} .compact-switch input`);
                if (checkbox && originalHexData[outputId]) {
                    const outputElement = document.getElementById(outputId);
                    outputElement.textContent = checkbox.checked ? 
                        originalHexData[outputId].replace(/\s/g, '') : 
                        originalHexData[outputId];
                }
            });
        });

        // 初始化显示请求解压标签页
        document.getElementById('requestTab').style.display = 'block';

        // Chunk解码相关函数
        function clearChunkInput() {
            document.getElementById('chunkInputText').value = '';
            document.getElementById('chunkError').style.display = 'none';
            document.getElementById('chunkOutputContainer').style.display = 'none';
        }

        // Chunk相关常量
        const CHUNK_SEPARATOR = '010000';  // 分隔符 01 00 00

        // 解析所有的数据块
        function parseChunks(hexData) {
            const chunks = [];
            let pos = 0;
            
            while (pos + 8 < hexData.length) {  // 确保至少有4字节分隔符+1字节大小
                // 检查分隔符（01 00 00）
                const separator = hexData.substr(pos, 6);
                if (separator !== CHUNK_SEPARATOR) {
                    console.error(`无效的分隔符: ${separator} at position ${pos}`);
                    break;
                }
                
                // 解析chunk大小（2字节）
                const chunkSize = parseInt(hexData.substr(pos + 6, 4), 16);
                if (chunkSize === 0) {  // 最后一个chunk
                    break;
                }
                
                // 提取chunk数据
                const dataStart = pos + 10;  // 跳过分隔符和大小值
                const chunkData = hexToBytes(hexData.substr(dataStart, chunkSize * 2));
                chunks.push({
                    size: chunkSize,
                    data: chunkData
                });
                
                // 移动到下一个chunk
                pos = dataStart + chunkSize * 2;
            }
            
            return chunks;
        }

        // 解码单个gzip压缩的数据块
        async function decodeGzipChunk(chunkData) {
            try {
                const decompressed = await decompressGzip(chunkData);
                const hexOutput = Array.from(decompressed)
                    .map(b => b.toString(16).padStart(2, '0'))
                    .join(' ')
                    .toUpperCase();
                return { hex: hexOutput};
            } catch (e) {
                console.error(`解压chunk失败: ${e}`);
                return null;
            }
        }

        // 修改processChunkInput函数
        async function processChunkInput() {
            // 更新当前激活的输入框ID
            activeInputId = 'chunkInputText';
            const input = document.getElementById('chunkInputText').value.trim();
            if (!input) {
                showChunkError("请输入数据");
                return;
            }

            try {
                // 转换为hex格式
                const hexData = convertToHex(input);
                if (!hexData) {
                    showChunkError("无法识别的数据格式");
                    return;
                }

                // 解析chunks
                const chunks = parseChunks(hexData);
                if (chunks.length === 0) {
                    showChunkError("未找到有效的数据块");
                    return;
                }

                // 解码每个chunk
                const combinedHex = [];

                for (const chunk of chunks) {
                    const result = await decodeGzipChunk(chunk.data);
                    if (result) {
                        combinedHex.push(result.hex);
                    }
                }
                
                if (combinedHex.length === 0) {
                    showChunkError("解压所有数据块失败");
                    return;
                }

                // 合并并显示结果
                const finalHex = combinedHex.join('');
                // 将hex转换为字节数组，然后转换为base64
                const finalBytes = hexToBytes(finalHex.replace(/\s/g, ''));
                const finalBase64 = arrayBufferToBase64(finalBytes);
                displayChunkResults(finalHex, finalBase64);

            } catch (err) {
                showChunkError("处理数据时出错: " + err.message);
            }
        }

        function showChunkError(message) {
            const error = document.getElementById('chunkError');
            error.textContent = message;
            error.style.display = 'block';
            setTimeout(() => error.classList.add('show'), 10);
            
            const container = document.getElementById('chunkOutputContainer');
            container.style.display = 'none';
            container.classList.remove('show');
        }

        // 修改displayChunkResults函数，根据复选框状态决定显示格式
        function displayChunkResults(hex, base64) {
            originalHexData['chunkHexOutput'] = hex;
            const checkbox = document.querySelector('#responseTab .compact-switch input');
            document.getElementById('chunkHexOutput').textContent = checkbox.checked ? 
                hex.replace(/\s/g, '') : hex;
            document.getElementById('chunkBase64Output').textContent = base64;
            
            const container = document.getElementById('chunkOutputContainer');
            container.style.display = 'block';
            setTimeout(() => container.classList.add('show'), 10);
            
            document.getElementById('chunkError').style.display = 'none';
            document.getElementById('chunkError').classList.remove('show');
            
            // 解码成功后显示弹窗提示
            showModal('successModal');
        }

        // 响应Chunk解压的拖放区域相关事件处理
        const chunkDropZone = document.getElementById('chunkDropZone');
        const chunkFileInput = document.getElementById('chunkFileInput');

        // 阻止默认拖放行为
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            chunkDropZone.addEventListener(eventName, preventDefaults, false);
        });

        // 拖放效果
        ['dragenter', 'dragover'].forEach(eventName => {
            chunkDropZone.addEventListener(eventName, highlightChunk, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            chunkDropZone.addEventListener(eventName, unhighlightChunk, false);
        });

        function highlightChunk(e) {
            chunkDropZone.classList.add('dragover');
        }

        function unhighlightChunk(e) {
            chunkDropZone.classList.remove('dragover');
        }

        // 处理文件拖放
        chunkDropZone.addEventListener('drop', handleChunkDrop, false);
        chunkDropZone.addEventListener('click', () => chunkFileInput.click());
        chunkFileInput.addEventListener('change', handleChunkFileSelect);

        function handleChunkDrop(e) {
            const dt = e.dataTransfer;
            const file = dt.files[0];
            processChunkFile(file);
        }

        function handleChunkFileSelect(e) {
            const file = e.target.files[0];
            processChunkFile(file);
            e.target.value = '';
        }

        // 处理Chunk文件
        function processChunkFile(file) {
            const reader = new FileReader();
            reader.onload = async function(e) {
                try {
                    const content = e.target.result;
                    // 将文件内容转换为hex格式
                    const hexData = convertToHex(content);
                    if (!hexData) {
                        showChunkError("无法识别的数据格式");
                        return;
                    }

                    // 解析chunks
                    const chunks = parseChunks(hexData);
                    if (chunks.length === 0) {
                        showChunkError("未找到有效的数据块");
                        return;
                    }

                    // 解码每个chunk
                    const combinedHex = [];

                    for (const chunk of chunks) {
                        const result = await decodeGzipChunk(chunk.data);
                        if (result) {
                            combinedHex.push(result.hex);
                        }
                    }
                    
                    if (combinedHex.length === 0) {
                        showChunkError("解压所有数据块失败");
                        return;
                    }

                    // 合并并显示结果
                    const finalHex = combinedHex.join('');
                    // 将hex转换为字节数组，然后转换为base64
                    const finalBytes = hexToBytes(finalHex.replace(/\s/g, ''));
                    const finalBase64 = arrayBufferToBase64(finalBytes);
                    displayChunkResults(finalHex, finalBase64);

                } catch (err) {
                    showChunkError("处理文件时出错: " + err.message);
                }
            };
            reader.readAsText(file);
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 监听输入框获取焦点事件
            document.getElementById('inputText').addEventListener('focus', function() {
                activeInputId = 'inputText';
                checkClipboard();
            });
            
            document.getElementById('chunkInputText').addEventListener('focus', function() {
                activeInputId = 'chunkInputText';
                checkClipboard();
            });
        });
    </script>
</body>
</html> 